package net.summerfarm.wnc.repository;

import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 自定义围栏区域ES测试示例
 * 演示正确的GeoJSON格式和测试方法
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceAreaEsTestExample {

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;

    /**
     * 测试保存简单矩形多边形
     * 这是最基础的测试，确保GeoJSON格式正确
     */
    @Test
    public void testSaveSimpleRectangle() {
        // 创建一个简单的矩形多边形（杭州市区域内的坐标）
        // 注意：多边形必须是闭合的，第一个点和最后一个点相同
        String geoJsonPolygon = "{\"type\":\"polygon\",\"coordinates\":[[[120.1,30.2],[120.2,30.2],[120.2,30.3],[120.1,30.3],[120.1,30.2]]]}";
        
        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("simple-rectangle-001");
        entity.setAdCodeMsgId(100);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 简单矩形多边形保存成功");
        } catch (Exception e) {
            System.err.println("✗ 保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试保存带孔洞的多边形
     * 演示如何创建带内部孔洞的复杂多边形
     */
    @Test
    public void testSavePolygonWithHole() {
        // 外环：大矩形
        // 内环：小矩形（孔洞）
        // 注意：内环的方向应该与外环相反（逆时针）
        String geoJsonPolygon = "{\"type\":\"polygon\",\"coordinates\":[" +
            "[[120.0,30.0],[120.4,30.0],[120.4,30.4],[120.0,30.4],[120.0,30.0]]," +  // 外环
            "[[120.15,30.15],[120.15,30.25],[120.25,30.25],[120.25,30.15],[120.15,30.15]]" +  // 内环（孔洞）
            "]}";
        
        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("polygon-with-hole-001");
        entity.setAdCodeMsgId(200);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 带孔洞的多边形保存成功");
        } catch (Exception e) {
            System.err.println("✗ 保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试保存复杂多边形（使用你的原始数据简化版）
     */
    @Test
    public void testSaveComplexPolygon() {
        // 使用你原始数据的简化版本（只取前几个点）
        String geoJsonPolygon = "{\"type\":\"polygon\",\"coordinates\":[[[120.272911,30.321279],[120.27259,30.320521],[120.272633,30.32045],[120.273528,30.32004],[120.275559,30.319851],[120.272911,30.321279]]]}";
        
        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("complex-polygon-001");
        entity.setAdCodeMsgId(300);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 复杂多边形保存成功");
        } catch (Exception e) {
            System.err.println("✗ 保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查询功能
     * 验证保存的多边形能否正确进行空间查询
     */
    @Test
    public void testSpatialQuery() {
        try {
            // 等待ES索引刷新
            Thread.sleep(2000);
            
            // 测试点在多边形内部的查询
            String testPoint = "120.15,30.25"; // 这个点应该在简单矩形内部
            Integer result = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(100), testPoint);
            
            System.out.println("查询结果: " + result);
            if (result != null && result.equals(100)) {
                System.out.println("✓ 空间查询成功");
            } else {
                System.out.println("✗ 空间查询失败");
            }
            
            // 测试点在多边形外部的查询
            String outsidePoint = "120.5,30.5"; // 这个点应该在多边形外部
            Integer outsideResult = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(100), outsidePoint);
            
            if (outsideResult == null) {
                System.out.println("✓ 外部点查询正确返回null");
            } else {
                System.out.println("✗ 外部点查询应该返回null，但返回了: " + outsideResult);
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 工具方法：将坐标字符串转换为GeoJSON格式
     * @param coordinatesStr 格式: "lng1,lat1;lng2,lat2;lng3,lat3;..."
     * @return GeoJSON格式的多边形字符串
     */
    public static String convertToGeoJsonPolygon(String coordinatesStr) {
        String[] points = coordinatesStr.split(";");
        StringBuilder coordinates = new StringBuilder();
        coordinates.append("[[");
        
        for (int i = 0; i < points.length; i++) {
            String[] lonLat = points[i].split(",");
            if (lonLat.length == 2) {
                coordinates.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < points.length - 1) {
                    coordinates.append(",");
                }
            }
        }
        
        coordinates.append("]]");
        
        return String.format("{\"type\":\"polygon\",\"coordinates\":%s}", coordinates.toString());
    }

    /**
     * 测试使用工具方法转换坐标
     */
    @Test
    public void testCoordinateConversion() {
        String coordinatesStr = "120.1,30.1;120.2,30.1;120.2,30.2;120.1,30.2;120.1,30.1";
        String geoJson = convertToGeoJsonPolygon(coordinatesStr);
        
        System.out.println("转换后的GeoJSON: " + geoJson);
        
        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("converted-polygon-001");
        entity.setAdCodeMsgId(400);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJson);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 坐标转换测试成功");
        } catch (Exception e) {
            System.err.println("✗ 坐标转换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
