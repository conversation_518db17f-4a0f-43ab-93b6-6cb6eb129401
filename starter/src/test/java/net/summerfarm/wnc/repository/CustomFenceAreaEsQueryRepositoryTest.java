package net.summerfarm.wnc.repository;

import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceAreaEsQueryRepositoryTest {

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;

    @Test
    public void matchEsByAdCodeMsgIdsWithPoi() {
        Integer i = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(Arrays.asList(1), "120.130396,30.259242");
        System.out.println(i);
    }
/*
    /**
     * 测试保存简单多边形围栏区域
     */
    @Test
    public void testSaveSimplePolygon() {
        // 创建一个简单的矩形多边形（杭州市区域内的坐标）
        String polygonCoordinates = "120.1,30.2;120.2,30.2;120.2,30.3;120.1,30.3;120.1,30.2";

        CustomFenceAreaEsEntity entity = createCustomFenceAreaEntity("simple-polygon-001", 100, polygonCoordinates);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("简单多边形围栏区域保存成功");
        } catch (Exception e) {
            System.err.println("保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试保存复杂多边形围栏区域
     */
    @Test
    public void testSaveComplexPolygon() {
        // 使用你原来的复杂坐标数据，但只取前几个点作为示例
        String complexCoordinates = "120.272911,30.321279;120.27259,30.320521;120.272633,30.32045;120.273528,30.32004;120.275559,30.319851;120.272911,30.321279";

        CustomFenceAreaEsEntity entity = createCustomFenceAreaEntity("complex-polygon-001", 200, complexCoordinates);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("复杂多边形围栏区域保存成功");
        } catch (Exception e) {
            System.err.println("保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试保存带孔洞的多边形围栏区域
     */
    @Test
    public void testSavePolygonWithHole() {
        // 外环：一个大的矩形
        String outerRing = "120.1,30.2;120.3,30.2;120.3,30.4;120.1,30.4;120.1,30.2";
        // 内环（孔洞）：一个小的矩形
        String innerRing = "120.15,30.25;120.25,30.25;120.25,30.35;120.15,30.35;120.15,30.25";

        CustomFenceAreaEsEntity entity = createCustomFenceAreaEntityWithHole("polygon-with-hole-001", 300, outerRing, innerRing);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("带孔洞的多边形围栏区域保存成功");
        } catch (Exception e) {
            System.err.println("保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建自定义围栏区域实体（简单多边形）
     */
    private CustomFenceAreaEsEntity createCustomFenceAreaEntity(String id, Integer adCodeMsgId, String coordinatesStr) {
        // 将坐标字符串转换为GeoJSON格式
        String geoJsonPolygon = convertToGeoJsonPolygon(coordinatesStr);

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId(id);
        entity.setAdCodeMsgId(adCodeMsgId);
        entity.setProvince("山东省");
        entity.setCity("日照市");
        entity.setArea("东港区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);

        return entity;
    }

    /**
     * 创建自定义围栏区域实体（带孔洞的多边形）
     */
    private CustomFenceAreaEsEntity createCustomFenceAreaEntityWithHole(String id, Integer adCodeMsgId,
                                                                       String outerRing, String innerRing) {
        // 将坐标字符串转换为带孔洞的GeoJSON格式
        String geoJsonPolygon = convertToGeoJsonPolygonWithHole(outerRing, innerRing);

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId(id);
        entity.setAdCodeMsgId(adCodeMsgId);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);

        return entity;
    }

    /**
     * 将坐标字符串转换为GeoJSON多边形格式
     * @param coordinatesStr 格式: "lng1,lat1;lng2,lat2;lng3,lat3;..."
     * @return GeoJSON格式的多边形字符串
     */
    private String convertToGeoJsonPolygon(String coordinatesStr) {
        String[] points = coordinatesStr.split(";");
        StringBuilder coordinates = new StringBuilder();
        coordinates.append("[[");

        for (int i = 0; i < points.length; i++) {
            String[] lonLat = points[i].split(",");
            if (lonLat.length == 2) {
                coordinates.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < points.length - 1) {
                    coordinates.append(",");
                }
            }
        }

        coordinates.append("]]");

        return String.format("{\"type\":\"polygon\",\"coordinates\":%s}", coordinates.toString());
    }

    /**
     * 将坐标字符串转换为带孔洞的GeoJSON多边形格式
     * @param outerRing 外环坐标
     * @param innerRing 内环坐标（孔洞）
     * @return GeoJSON格式的多边形字符串
     */
    private String convertToGeoJsonPolygonWithHole(String outerRing, String innerRing) {
        // 构建外环
        String[] outerPoints = outerRing.split(";");
        StringBuilder outerCoords = new StringBuilder();
        outerCoords.append("[");

        for (int i = 0; i < outerPoints.length; i++) {
            String[] lonLat = outerPoints[i].split(",");
            if (lonLat.length == 2) {
                outerCoords.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < outerPoints.length - 1) {
                    outerCoords.append(",");
                }
            }
        }
        outerCoords.append("]");

        // 构建内环（孔洞）
        String[] innerPoints = innerRing.split(";");
        StringBuilder innerCoords = new StringBuilder();
        innerCoords.append("[");

        for (int i = 0; i < innerPoints.length; i++) {
            String[] lonLat = innerPoints[i].split(",");
            if (lonLat.length == 2) {
                innerCoords.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < innerPoints.length - 1) {
                    innerCoords.append(",");
                }
            }
        }
        innerCoords.append("]");

        // 组合外环和内环
        String coordinates = String.format("[%s,%s]", outerCoords.toString(), innerCoords.toString());

        return String.format("{\"type\":\"polygon\",\"coordinates\":%s}", coordinates);
    }

    /**
     * 测试查询功能 - 验证保存的数据能否正确查询
     */
    @Test
    public void testQuerySavedPolygon() {
        // 先保存一个测试数据
        String testCoordinates = "120.15,30.25;120.25,30.25;120.25,30.35;120.15,30.35;120.15,30.25";
        CustomFenceAreaEsEntity entity = createCustomFenceAreaEntity("query-test-001", 400, testCoordinates);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("测试数据保存成功");

            // 等待ES索引刷新
            Thread.sleep(2000);

            // 测试点在多边形内部的查询
            String testPoint = "120.2,30.3"; // 这个点应该在多边形内部
            Integer result = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(400), testPoint);

            System.out.println("查询结果: " + result);
            if (result != null && result.equals(400)) {
                System.out.println("✓ 点在多边形内部查询成功");
            } else {
                System.out.println("✗ 点在多边形内部查询失败");
            }

        } catch (Exception e) {
            System.err.println("测试查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试边界情况 - 点在多边形外部
     */
    @Test
    public void testPointOutsidePolygon() {
        try {
            // 测试点在多边形外部的查询
            String testPoint = "120.5,30.5"; // 这个点应该在多边形外部
            Integer result = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(400), testPoint);

            System.out.println("外部点查询结果: " + result);
            if (result == null) {
                System.out.println("✓ 点在多边形外部查询正确返回null");
            } else {
                System.out.println("✗ 点在多边形外部查询应该返回null");
            }

        } catch (Exception e) {
            System.err.println("测试外部点查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试使用你原始数据的完整多边形（简化版本）
     */
    @Test
    public void testOriginalDataSimplified() {
        // 使用你原始数据的前20个点作为测试
        String originalData = "120.272911,30.321279;120.27259,30.320521;120.272633,30.32045;120.273528,30.32004;120.275559,30.319851;120.277843,30.319577;120.285424,30.318669;120.285597,30.318647;120.291956,30.317819;120.291626,30.315325;120.29346,30.31509;120.293469,30.314543;120.293256,30.313633;120.29347,30.312876;120.293788,30.31252;120.294351,30.309899;120.294544,30.309666;120.294657,30.309241;120.294723,30.308296;120.272911,30.321279";

        CustomFenceAreaEsEntity entity = createCustomFenceAreaEntity("original-data-simplified", 500, originalData);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("原始数据简化版保存成功");
        } catch (Exception e) {
            System.err.println("原始数据简化版保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试多孔洞多边形
     */
    @Test
    public void testPolygonWithMultipleHoles() {
        // 外环：大矩形
        String outerRing = "120.0,30.0;120.4,30.0;120.4,30.4;120.0,30.4;120.0,30.0";
        // 第一个孔洞
        String hole1 = "120.05,30.05;120.15,30.05;120.15,30.15;120.05,30.15;120.05,30.05";
        // 第二个孔洞
        String hole2 = "120.25,30.25;120.35,30.25;120.35,30.35;120.25,30.35;120.25,30.25";

        String geoJson = convertToGeoJsonPolygonWithMultipleHoles(outerRing, Arrays.asList(hole1, hole2));

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("multi-holes-001");
        entity.setAdCodeMsgId(600);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJson);
        entity.setStatus(0);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("多孔洞多边形保存成功");
        } catch (Exception e) {
            System.err.println("多孔洞多边形保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 将坐标字符串转换为带多个孔洞的GeoJSON多边形格式
     */
    private String convertToGeoJsonPolygonWithMultipleHoles(String outerRing, List<String> holes) {
        // 构建外环
        String[] outerPoints = outerRing.split(";");
        StringBuilder outerCoords = new StringBuilder();
        outerCoords.append("[");

        for (int i = 0; i < outerPoints.length; i++) {
            String[] lonLat = outerPoints[i].split(",");
            if (lonLat.length == 2) {
                outerCoords.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < outerPoints.length - 1) {
                    outerCoords.append(",");
                }
            }
        }
        outerCoords.append("]");

        // 构建所有孔洞
        StringBuilder allRings = new StringBuilder();
        allRings.append("[").append(outerCoords.toString());

        for (String hole : holes) {
            String[] holePoints = hole.split(";");
            StringBuilder holeCoords = new StringBuilder();
            holeCoords.append(",[");

            for (int i = 0; i < holePoints.length; i++) {
                String[] lonLat = holePoints[i].split(",");
                if (lonLat.length == 2) {
                    holeCoords.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                    if (i < holePoints.length - 1) {
                        holeCoords.append(",");
                    }
                }
            }
            holeCoords.append("]");
            allRings.append(holeCoords.toString());
        }

        allRings.append("]");

        return String.format("{\"type\":\"polygon\",\"coordinates\":%s}", allRings.toString());
    }
}
