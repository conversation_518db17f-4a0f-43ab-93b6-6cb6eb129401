package net.summerfarm.wnc.infrastructure.es.converter;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.entity.HistoryOutOfCustomFenceAreaEntity;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.document.HistoryOutOfCustomFenceAreaDoc;

/**
 * 转换类
 * date: 2025/9/3 14:53<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceAreaConverter {

    public static CustomFenceAreaEsEntity model2Entity(CustomFenceArea customFenceArea) {
        if (customFenceArea == null) {
            return null;
        }
        CustomFenceAreaEsEntity customFenceAreaEntity = new CustomFenceAreaEsEntity();
        customFenceAreaEntity.setId(customFenceArea.getId());
        customFenceAreaEntity.setAdCodeMsgId(customFenceArea.getAdCodeMsgId() != null ? Integer.parseInt(customFenceArea.getAdCodeMsgId()) : null);
        customFenceAreaEntity.setProvince(customFenceArea.getProvince());
        customFenceAreaEntity.setCity(customFenceArea.getCity());
        customFenceAreaEntity.setArea(customFenceArea.getArea());
        customFenceAreaEntity.setGeoShape(customFenceArea.getGeoShape());
        customFenceAreaEntity.setStatus(customFenceArea.getStatus());
        return customFenceAreaEntity;
    }


    public static CustomFenceArea entity2Model(CustomFenceAreaEsEntity customFenceAreaEsEntity) {
        if (customFenceAreaEsEntity == null) {
            return null;
        }
        CustomFenceArea customFenceArea = new CustomFenceArea();

        customFenceArea.setId(customFenceAreaEsEntity.getId());
        customFenceArea.setAdCodeMsgId(String.valueOf(customFenceAreaEsEntity.getAdCodeMsgId()));
        customFenceArea.setProvince(customFenceAreaEsEntity.getProvince());
        customFenceArea.setCity(customFenceAreaEsEntity.getCity());
        customFenceArea.setArea(customFenceAreaEsEntity.getArea());
        customFenceArea.setPolygonFromFrontend(customFenceAreaEsEntity.getGeoShape());
        customFenceArea.setStatus(customFenceAreaEsEntity.getStatus());

        return customFenceArea;
    }
}
