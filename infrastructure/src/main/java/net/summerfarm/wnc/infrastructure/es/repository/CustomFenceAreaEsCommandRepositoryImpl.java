package net.summerfarm.wnc.infrastructure.es.repository;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.infrastructure.es.converter.CustomFenceAreaConverter;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaEsMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义围栏区域
 * date: 2025/9/3 11:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaEsCommandRepositoryImpl implements CustomFenceAreaEsCommandRepository {

    @Resource
    private CustomFenceAreaEsMapper customFenceAreaEsMapper;

    @Override
    public void saveAll(List<CustomFenceAreaEsEntity> customFenceAreaEsEntities) {
        if (CollectionUtils.isEmpty(customFenceAreaEsEntities)) {
            return;
        }

        List<CustomFenceArea> customFenceAreas = customFenceAreaEsEntities.stream().map(CustomFenceAreaConverter::entity2Model).collect(Collectors.toList());
        try {
            customFenceAreaEsMapper.insertBatch(customFenceAreas);
        } catch (Exception e) {
            List<String> ids = customFenceAreas.stream().map(CustomFenceArea::getId).collect(Collectors.toList());
            customFenceAreaEsMapper.deleteBatchIds(ids);

            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
